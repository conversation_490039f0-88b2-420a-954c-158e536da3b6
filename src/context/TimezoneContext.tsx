import React, { createContext, useContext, useEffect, useState } from 'react';

import {
  formatTimestamp,
  isValidTimezone,
  TimestampFormat,
} from '@/utils/timezone';

import { useAuth } from './AuthContext';

interface TimezoneContextType {
  userTimezone: string;
  isTimezoneLoaded: boolean;
  formatUserTimestamp: (
    utcTimestamp: string | Date,
    format?: TimestampFormat
  ) => string;
  getCurrentUserTimestamp: (format?: TimestampFormat) => string;
}

const TimezoneContext = createContext<TimezoneContextType | undefined>(
  undefined
);

interface TimezoneProviderProps {
  children: React.ReactNode;
}

export const TimezoneProvider: React.FC<TimezoneProviderProps> = ({
  children,
}) => {
  const [userTimezone, setUserTimezone] = useState<string>('UTC');
  const [isTimezoneLoaded, setIsTimezoneLoaded] = useState<boolean>(false);
  const { user, isAuthenticated } = useAuth();

  // Load user timezone from user profile when available
  useEffect(() => {
    if (isAuthenticated && user?.timezone) {
      const timezone = user.timezone;

      // Validate the timezone before setting it
      if (isValidTimezone(timezone)) {
        setUserTimezone(timezone);
        setIsTimezoneLoaded(true);
      } else {
        console.warn(
          `Invalid timezone from user profile: ${timezone}, falling back to UTC`
        );
        setUserTimezone('UTC');
        setIsTimezoneLoaded(true);
      }
    } else if (isAuthenticated && !user?.timezone) {
      // User is authenticated but no timezone set, use UTC as fallback
      setUserTimezone('UTC');
      setIsTimezoneLoaded(true);
    } else if (!isAuthenticated) {
      // User not authenticated, reset to UTC
      setUserTimezone('UTC');
      setIsTimezoneLoaded(false);
    }
  }, [isAuthenticated, user?.timezone]);

  // Fallback to browser timezone if user timezone is not available
  useEffect(() => {
    if (!isAuthenticated && !isTimezoneLoaded) {
      try {
        const browserTimezone =
          Intl.DateTimeFormat().resolvedOptions().timeZone;
        if (isValidTimezone(browserTimezone)) {
          setUserTimezone(browserTimezone);
        } else {
          setUserTimezone('UTC');
        }
      } catch (error) {
        console.warn('Failed to detect browser timezone, using UTC:', error);
        setUserTimezone('UTC');
      }
      setIsTimezoneLoaded(true);
    }
  }, [isAuthenticated, isTimezoneLoaded]);

  // Helper function to format timestamps using user's timezone
  const formatUserTimestamp = (
    utcTimestamp: string | Date,
    format: TimestampFormat = 'full'
  ): string => {
    return formatTimestamp(utcTimestamp, userTimezone, format);
  };

  // Helper function to get current timestamp in user's timezone
  const getCurrentUserTimestamp = (
    format: TimestampFormat = 'full'
  ): string => {
    return formatTimestamp(new Date(), userTimezone, format);
  };

  const value: TimezoneContextType = {
    userTimezone,
    isTimezoneLoaded,
    formatUserTimestamp,
    getCurrentUserTimestamp,
  };

  return (
    <TimezoneContext.Provider value={value}>
      {children}
    </TimezoneContext.Provider>
  );
};

export const useTimezone = (): TimezoneContextType => {
  const context = useContext(TimezoneContext);
  if (context === undefined) {
    throw new Error('useTimezone must be used within a TimezoneProvider');
  }
  return context;
};

// Export the context for testing purposes
export { TimezoneContext };
