import { useQueryClient } from '@tanstack/react-query';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown, ChevronLeft, Plus, Settings } from 'lucide-react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { Link, useNavigate } from 'react-router-dom';

import { Icons } from '@/assets/icons/DashboardIcons';
import CompanyNameModal from '@/components/common/CompanyNameModal';
import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import { Spinner } from '@/components/common/Loader';
import { NotificationContainer } from '@/components/ui';
import { ROUTES } from '@/constants/routes';
import { useTenant } from '@/context/TenantContext';
import { agentSuites as mockAgentsSuite } from '@/data/constants';
import { useGetAIAgentSuites } from '@/hooks/useAgents';
import { useHeartbeat } from '@/hooks/useHeartbeat';
import { useNotifications } from '@/hooks/useNotifications';
import { useMyJoinRequests } from '@/hooks/useRequestToJoin';
import { useGetUserProfile } from '@/hooks/useUserProfile';
import { AgentCard } from '@/pages/AiAgentsPage';
import { useClaimAgentSuiteApi } from '@/services/upivotalAgenticService';
import { AIAgent } from '@/types/agents';
import { UserBasicInfoPayload } from '@/types/user';
import { GET_USER_QUERY } from '@/utils/queryKeys';

export type PageType =
  | 'ai-agents'
  | 'business-stack'
  | 'analytics'
  | 'knowledge-base';

interface ActivateSuitePageProps {
  pageType: PageType;
  suiteId: string;
}

const ActivateSuitePage: React.FC<ActivateSuitePageProps> = ({
  pageType,
  suiteId,
}) => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // Initialize notification system
  const { notify, notifications, dismiss } = useNotifications();

  // Primary data flow: API-based approach
  const { data: agentSuites = [], isLoading: isLoadingSuites } =
    useGetAIAgentSuites();
  const suite = agentSuites.find(s => s.agentSuiteKey === suiteId);

  // Get user data to check if suite is already claimed
  const { data: userData, isLoading: isLoadingUser } =
    useGetUserProfile<UserBasicInfoPayload>();

  // Get user's join requests to check if they've already requested to join this suite
  const { data: myJoinRequestsData } = useMyJoinRequests(
    { page: 0, pageSize: 100 },
    true
  );

  // Heartbeat functionality
  const {
    heartbeatState,
    fetchHeartbeats,
    initializeHeartbeat,
    pauseHeartbeat,
    getHeartbeatStatus,
  } = useHeartbeat();

  // Use API data only - no longer relying on location.state
  const finalSuite = suite;

  // Get agents that belong to this suite (alphabetically)
  const suiteAgents: AIAgent[] = [...(finalSuite?.availableAgents || [])].sort(
    (a, b) =>
      (a.agentName || '').localeCompare(b.agentName || '', undefined, {
        sensitivity: 'base',
      })
  );
  const suiteFallbackImage = mockAgentsSuite.filter(
    mockAgent =>
      mockAgent.id.toLowerCase() === finalSuite?.agentSuiteKey.toLowerCase()
  )[0]?.image;

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [chatMessage, setChatMessage] = useState<string>('');
  const [isSettingsDropdownOpen, setIsSettingsDropdownOpen] = useState(false);
  const [settingsButtonRect, setSettingsButtonRect] = useState<DOMRect | null>(
    null
  );
  const [showCompanyNameModal, setShowCompanyNameModal] = useState(false);
  const settingsDropdownRef = useRef<HTMLDivElement>(null);
  const settingsButtonRef = useRef<HTMLButtonElement>(null);
  const { activeAgent, setActiveAgent, setTenantId } = useTenant();

  const claimAgentsSuite = useClaimAgentSuiteApi();

  // Fetch heartbeat data when component mounts
  useEffect(() => {
    fetchHeartbeats();
  }, [fetchHeartbeats]);

  // Handle heartbeat actions
  const handleHeartbeatAction = async (
    action: 'initialize' | 'pause',
    agentKey: string
  ): Promise<void> => {
    try {
      if (action === 'initialize') {
        await initializeHeartbeat(agentKey);
      } else {
        await pauseHeartbeat(agentKey);
      }
    } catch (error) {
      // Error handling is done in the useHeartbeat hook
      console.error('Heartbeat action failed:', error);
    }
  };
  const claimedSuites = userData?.userInfo?.tenant?.claimedAgentSuites;

  const isMatchingSuite = useMemo(() => {
    return claimedSuites?.some(
      claimedSuite => claimedSuite.suite.agentSuiteKey === suiteId
    );
  }, [claimedSuites, suiteId]);
  // Check if current user is a member of THIS specific suite and get user role
  const currentUserSuiteInfo = claimedSuites?.find(claimedSuite => {
    // First check if this is the current suite being viewed
    const isCurrentSuite = claimedSuite.suite.agentSuiteKey === suiteId;
    // Then check if user is a member of this suite
    const isUserMember = claimedSuite.members.some(
      member => member?.user?.userId === userData?.userInfo?.userId
    );
    // Both conditions must be true
    return isCurrentSuite && isUserMember;
  });
  const isUserMember = !!currentUserSuiteInfo;

  // Get current user's role in this suite
  const currentUserRole =
    currentUserSuiteInfo?.members?.find(
      member => member?.user?.userId === userData?.userInfo?.userId
    )?.memberRoles?.[0] || 'MEMBER';
  // Check if user can manage members (manager or lead roles)
  const canManageMembers = currentUserRole === 'MANAGER';

  // Check if user has already requested to join this suite
  const hasPendingRequest = useMemo(() => {
    if (!myJoinRequestsData?.requests || !finalSuite) return false;
    return myJoinRequestsData.requests.some(
      request =>
        request.agentSuiteKey === finalSuite.agentSuiteKey &&
        request.status === 'PENDING'
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [myJoinRequestsData?.requests, finalSuite?.agentSuiteKey]);

  const handleAgentSelect = (agentKey: string) => {
    setActiveAgent(agentKey);
  };

  // Dynamic navigation based on pageType
  const getSuccessRoute = () => {
    switch (pageType) {
      case 'business-stack':
        return ROUTES.DASHBOARD_BUSINESS_STACK;
      case 'analytics':
        return `${ROUTES.DASHBOARD_ANALYTICS_INSIGHTS}`;
      case 'ai-agents':
      case 'knowledge-base':
        return ROUTES.DASHBOARD_KNOWLEDGE_BASE;
      default:
        return ROUTES.DASHBOARD_BASE;
    }
  };

  // Dynamic breadcrumb configuration
  const getBreadcrumbConfig = () => {
    switch (pageType) {
      case 'ai-agents':
        return {
          text: 'Agents Suites',
          action: () => navigate(ROUTES.DASHBOARD_AI_AGENTS),
        };
      case 'knowledge-base':
        return {
          text: 'Knowledge Base',
          action: () => navigate(-1),
        };
      default:
        return {
          text: 'Agents Suites',
          action: () => navigate(-1),
        };
    }
  };

  const handleClaimAgentSuite = async (agentSuiteKey: string) => {
    try {
      setIsLoading(true);
      setChatMessage('');

      const response = await claimAgentsSuite(agentSuiteKey);

      if (response.status === true) {
        // Set the tenant ID from the response
        setTenantId(response.data.tenantId);

        const message =
          response.message || 'Agents suite claimed successfully!';

        notify(message, 'success');

        // Invalidate user profile queries to refresh claimed suites data
        await queryClient.invalidateQueries({ queryKey: [GET_USER_QUERY] });

        // Force refetch with a slight delay to ensure backend consistency
        setTimeout(async () => {
          await queryClient.refetchQueries({ queryKey: [GET_USER_QUERY] });
        }, 1500);

        navigate(getSuccessRoute());
      } else {
        const errorMessage = response.message || 'Failed to claim agents suite';
        notify(errorMessage);
      }
    } catch (error: unknown) {
      const errorMessage =
        typeof error === 'object' && error !== null && 'response' in error
          ? (error as any).response?.data?.message
          : error instanceof Error
            ? error.message
            : 'Failed to claim agents suite. Please try again.';

      notify(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClaimSuiteClick = () => {
    setShowCompanyNameModal(true);
  };

  const handleCompanyNameSubmit = (_companyName: string) => {
    setShowCompanyNameModal(false);
    if (finalSuite) {
      handleClaimAgentSuite(finalSuite.agentSuiteKey);
    }
  };

  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);

  // Handle click outside for settings dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        settingsDropdownRef.current &&
        !settingsDropdownRef.current.contains(event.target as Node) &&
        settingsButtonRef.current &&
        !settingsButtonRef.current.contains(event.target as Node)
      ) {
        setIsSettingsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (!activeAgent) return;

    const reloadForAgent = async () => {
      try {
        if (reloadChatHistoryRef.current) {
          await reloadChatHistoryRef.current();
        }
      } catch (error) {
        console.error(error);
      }
    };

    reloadForAgent();
  }, [activeAgent]);

  // Toggle settings dropdown
  const toggleSettingsDropdown = () => {
    if (!isSettingsDropdownOpen && settingsButtonRef.current) {
      setSettingsButtonRect(settingsButtonRef.current.getBoundingClientRect());
    }
    setIsSettingsDropdownOpen(!isSettingsDropdownOpen);
  };

  // Settings dropdown handlers
  const handleAddMembers = () => {
    setIsSettingsDropdownOpen(false);
    navigate(ROUTES.DASHBOARD_MEMBERS_INVITE, {
      state: {
        agentSuiteKey: finalSuite?.agentSuiteKey,
        returnRoute: getSuccessRoute(),
      },
    });
  };

  // const handleCompanyInfo = () => {
  //   setIsSettingsDropdownOpen(false);
  //   if (finalSuite) {
  //     navigate(ROUTES.DASHBOARD_SETTINGS_COMPANY_INFO, {
  //       state: {
  //         agentSuiteKey: finalSuite.agentSuiteKey,
  //         suiteName: finalSuite.agentSuiteName,
  //       },
  //     });
  //   }
  // };

  // const handleDepartmentInfo = () => {
  //   setIsSettingsDropdownOpen(false);
  //   if (finalSuite) {
  //     navigate(ROUTES.DASHBOARD_SETTINGS_DEPARTMENT_INFO, {
  //       state: {
  //         agentSuiteKey: finalSuite.agentSuiteKey,
  //         suiteName: finalSuite.agentSuiteName,
  //       },
  //     });
  //   }
  // };

  // Show loading state while fetching data
  if (isLoadingSuites || isLoadingUser) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <Spinner className="mx-auto mb-4 h-8 w-8" />
          <p className="text-lg text-gray-600">Loading agents suite...</p>
        </div>
      </div>
    );
  }

  // Show error state if suite not found
  if (!finalSuite) {
    const breadcrumbConfig = getBreadcrumbConfig();
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h1 className="mb-4 text-2xl font-bold text-black">
            Agents Suite Not Found
          </h1>
          <p className="mb-4 text-gray-600">
            The agents suite with ID "{suiteId}" could not be found.
          </p>
          <button
            onClick={breadcrumbConfig.action}
            className="rounded-lg bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-orange-15"
          >
            {pageType === 'ai-agents' ? 'Back to Agents Hub' : 'Go Back'}
          </button>
        </div>
      </div>
    );
  }

  const breadcrumbConfig = getBreadcrumbConfig();

  return (
    <div className="flex h-full">
      {/* Chat Sidebar - LEFT SIDE */}
      <EnhancedChatSidebar
        reloadChatHistoryRef={reloadChatHistoryRef}
        externalInjectedMessage={chatMessage}
        className="hidden md:block"
      />

      {/* Main Content - RIGHT SIDE */}
      <div className="flex-1 overflow-y-auto">
        <div className="flex w-full max-w-[850px] flex-col gap-4 p-4 sm:p-8">
          <div className="flex items-center justify-between gap-4">
            {/* Breadcrumb */}
            {pageType === 'ai-agents' ? (
              <Link
                to={ROUTES.DASHBOARD_AI_AGENTS}
                className="flex items-center gap-1 font-semibold text-blackTwo"
              >
                <ChevronLeft
                  className="h-4 w-4 sm:h-6 sm:w-6"
                  strokeWidth={2}
                />
                {breadcrumbConfig.text}
              </Link>
            ) : (
              <button
                onClick={breadcrumbConfig.action}
                className="flex items-center gap-1 font-semibold text-blackTwo"
              >
                <ChevronLeft
                  className="h-4 w-4 sm:h-6 sm:w-6"
                  strokeWidth={2}
                />
                {breadcrumbConfig.text}
              </button>
            )}
            {isMatchingSuite && isUserMember && canManageMembers && (
              <>
                <button
                  ref={settingsButtonRef}
                  onClick={toggleSettingsDropdown}
                  className="flex h-[42px] items-center gap-2 rounded-lg border-[1.5px] border-primary bg-[#FFF1EB] px-4 text-sm font-normal text-black transition-colors sm:bg-primary sm:text-base sm:text-white sm:hover:bg-primary/90"
                >
                  <Settings className="h-4 w-4 sm:h-5 sm:w-5" strokeWidth={2} />
                  <span className="hidden sm:block">Suite Settings</span>
                  <ChevronDown
                    className={`h-4 w-4 transition-transform ${isSettingsDropdownOpen ? 'rotate-180' : ''}`}
                    strokeWidth={2}
                  />
                </button>

                {/* Settings Dropdown */}
                {isSettingsDropdownOpen &&
                  settingsButtonRect &&
                  createPortal(
                    <AnimatePresence>
                      <motion.div
                        ref={settingsDropdownRef}
                        initial={{ opacity: 0, scale: 0.95, y: 0 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        exit={{ opacity: 0, scale: 0.95, y: 0 }}
                        transition={{ duration: 0.15, ease: 'easeOut' }}
                        className="fixed z-[9999] w-[200px] overflow-hidden rounded-xl border bg-white shadow-lg"
                        style={{
                          top: settingsButtonRect.bottom + 8,
                          right: window.innerWidth - settingsButtonRect.right,
                        }}
                      >
                        <div className="py-2">
                          <button
                            onClick={handleAddMembers}
                            className="flex w-full items-center px-4 py-3 text-sm transition-colors hover:bg-[#FFECE3]"
                          >
                            <Icons.Users className="mr-3 h-4 w-4" />
                            Add Members
                          </button>
                          {/* <button
                            onClick={handleCompanyInfo}
                            className="flex w-full items-center px-4 py-3 text-sm transition-colors hover:bg-[#FFECE3]"
                          >
                            <Icons.Building className="mr-3 h-4 w-4" />
                            Company Info
                          </button>
                          <button
                            onClick={handleDepartmentInfo}
                            className="flex w-full items-center px-4 py-3 text-sm transition-colors hover:bg-[#FFECE3]"
                          >
                            <Icons.Hierarchy className="mr-3 h-4 w-4" />
                            Department Info
                          </button> */}
                        </div>
                      </motion.div>
                    </AnimatePresence>,
                    document.body
                  )}
              </>
            )}
            {!isUserMember && isMatchingSuite && (
              <button
                className="flex w-fit items-center gap-2 rounded-lg bg-primary px-4 py-3 font-normal text-white transition-colors hover:bg-orange-15 disabled:cursor-not-allowed disabled:opacity-50"
                onClick={() => {
                  if (finalSuite && !hasPendingRequest) {
                    navigate(ROUTES.DASHBOARD_MEMBERS_REQUEST_TO_JOIN, {
                      state: {
                        agentSuiteKey: finalSuite.agentSuiteKey,
                        suiteName: finalSuite.agentSuiteName,
                        returnRoute: getSuccessRoute(),
                      },
                    });
                  }
                }}
                disabled={hasPendingRequest}
              >
                {!hasPendingRequest && <Plus className="h-5 w-5" />}
                {hasPendingRequest ? 'Request Pending' : 'Request To Join'}
              </button>
            )}
          </div>

          {/* Notifications Container */}
          <NotificationContainer
            notifications={notifications}
            onClose={dismiss}
            className="w-full"
            maxNotifications={3}
          />

          {!isMatchingSuite && (
            <div className="mb-2 flex flex-col items-start justify-between gap-4 rounded-xl border border-[#FFE0D1] bg-peachTwo p-4 sm:flex-row sm:items-center sm:p-5 lg:h-[116px]">
              <div className="flex items-center gap-2">
                <div className="hidden h-[46px] w-[46px] shrink-0 items-center justify-center gap-2 rounded-full border border-primary sm:flex">
                  <Icons.ClaimIcon className="h-[24px] w-[18px]" />
                </div>
                <div>
                  <div className="font-spartan text-base font-semibold text-blackOne sm:font-medium">
                    Claim Your Suite
                  </div>
                  <p className="mt-1 font-inter text-sm text-subText">
                    Start using your AI agents immediately by claiming this
                    suite
                  </p>
                </div>
              </div>
              <button
                className="rounded-lg bg-primary px-4 py-3 font-normal text-white transition-colors hover:bg-orange-15 disabled:opacity-30"
                onClick={handleClaimSuiteClick}
                disabled={isLoading || !finalSuite}
              >
                <div className="flex items-center gap-2">
                  {isLoading && (
                    <Spinner className="h-4 w-4 border-white border-b-[transparent]" />
                  )}
                  <span>Claim this suite</span>
                </div>
              </button>
            </div>
          )}

          {/* Suite Header */}
          <div className="relative h-[198px] overflow-hidden rounded-xl bg-cover bg-center font-spartan">
            {/* Background Image */}
            <div
              className="absolute inset-0 bg-cover bg-center"
              style={{
                backgroundImage: finalSuite.avatar
                  ? `url(${finalSuite.avatar})`
                  : `url(${suiteFallbackImage})`,
              }}
            />
            {/* Dark Overlay */}
            <div className="absolute inset-0 bg-black/20" />
            {/* Content */}
            <div className="relative z-10 flex h-full flex-col justify-center p-6">
              <div className="flex w-fit items-center justify-center rounded-lg bg-white px-4 py-3">
                <h1 className="mt-1 text-lg font-bold leading-none sm:text-[30px]">
                  {finalSuite.agentSuiteName}
                </h1>
              </div>
              <h2 className="mt-4 w-fit text-base font-semibold text-white sm:mt-8 sm:text-xl">
                {finalSuite.description}
              </h2>
              <div className="font-inter text-sm text-white sm:text-lg">
                {finalSuite.roleDescription}
              </div>
            </div>
          </div>

          <h1 className="text-base font-semibold sm:text-lg">
            What’s Inside Your Suite
          </h1>
          {/* Agents Grid */}
          {suiteAgents.length > 0 && (
            <div className="grid w-full grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-2">
              {suiteAgents.map(agent => (
                <AgentCard
                  key={agent.agentKey}
                  agent={agent}
                  showChatButton={true}
                  showHeartbeatControl={false}
                  heartbeatStatus={getHeartbeatStatus(agent.agentKey)}
                  onHeartbeatAction={handleHeartbeatAction}
                  isHeartbeatLoading={
                    heartbeatState.loadingAgent === agent.agentKey
                  }
                  isActiveAgent={activeAgent === agent.agentKey}
                  link={
                    agent.agentKey
                      ? ROUTES.DASHBOARD_AGENT_ACTIVATION_AGENT(agent.agentKey)
                      : '#'
                  }
                  onAgentSelect={() => handleAgentSelect(agent.agentKey)}
                />
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Company Name Modal */}
      <CompanyNameModal
        isOpen={showCompanyNameModal}
        onClose={() => setShowCompanyNameModal(false)}
        onProceed={handleCompanyNameSubmit}
        suiteName={finalSuite?.agentSuiteName}
      />
    </div>
  );
};

export default ActivateSuitePage;
