import { ChevronDown } from 'lucide-react';
import React from 'react';

import { Icons } from '@/assets/icons/DashboardIcons';

import CustomDropdown from '../common/CustomDropdown';

interface FilterPanelProps {
  activeTab: string;
  onDateFilterClick: () => void;
  onPrioritySelection: (values: string[]) => void;
  onStatusSelection: (values: string[]) => void;
  currentSearchParams: URLSearchParams;
  dateButtonRef?: React.RefObject<HTMLDivElement>;
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  activeTab,
  onDateFilterClick,
  onPrioritySelection,
  onStatusSelection,
  currentSearchParams,
  dateButtonRef,
}) => {
  // Priority options for task logs
  const priorityOptions = [
    { id: 'low', label: 'Low', value: 'LOW' },
    { id: 'medium', label: 'Medium', value: 'MEDIUM' },
    { id: 'high', label: 'High', value: 'HIGH' },
  ];

  // Status options for task logs and assignment logs
  const statusOptions = [
    { id: 'pending', label: 'Pending', value: 'PENDING' },
    { id: 'in-progress', label: 'In Progress', value: 'IN_PROGRESS' },
    { id: 'completed', label: 'Completed', value: 'COMPLETED' },
    { id: 'cancelled', label: 'Cancelled', value: 'CANCELLED' },
  ];

  const getCurrentStatusSelections = () => {
    const statusParam = currentSearchParams.get('status');
    return statusParam ? statusParam.split(',') : [];
  };

  const getCurrentPrioritySelections = () => {
    const priorityParam = currentSearchParams.get('priority');
    return priorityParam ? priorityParam.split(',') : [];
  };

  return (
    <div className="grid w-full grid-cols-3 gap-4">
      {/* Date Filter */}
      <div className="relative" ref={dateButtonRef}>
        <button
          onClick={onDateFilterClick}
          className="flex h-[32px] w-full items-center justify-between gap-3 rounded-lg border bg-white px-4 py-2 text-sm text-grayTen sm:h-[44px]"
        >
          <div className="flex items-center gap-2">
            <Icons.DataDate className="h-4 w-4 sm:h-5 sm:w-5" />
            <span>Date</span>
          </div>
          <ChevronDown className="h-4 w-4" />
        </button>
      </div>

      {/* Priority Filter - Only show for task-logs */}
      {activeTab === 'task-logs' && (
        <CustomDropdown
          options={priorityOptions}
          selectedValues={getCurrentPrioritySelections()}
          onSelectionChange={onPrioritySelection}
          buttonText="Priority"
          icon={<Icons.OutlineTransaction className="h-4 w-4 sm:h-5 sm:w-5" />}
          className="relative"
          isMultiSelect={false}
          buttonClassName="flex h-[32px] sm:h-[44px] w-full items-center justify-between gap-3 rounded-lg border bg-white px-4 py-2 text-sm text-grayTen"
          fullWidth={true}
          variant="indicator"
        />
      )}

      {/* Status Filter - Show for task-logs and assignment-logs */}
      {(activeTab === 'task-logs' || activeTab === 'assignment-logs') && (
        <CustomDropdown
          options={statusOptions}
          selectedValues={getCurrentStatusSelections()}
          onSelectionChange={onStatusSelection}
          buttonText="Status"
          icon={<Icons.StatusFilled className="h-4 w-4 sm:h-5 sm:w-5" />}
          className="relative"
          isMultiSelect={false}
          buttonClassName="flex h-[32px] sm:h-[44px] w-full items-center justify-between gap-3 rounded-lg border bg-white px-4 py-2 text-sm text-grayTen"
          fullWidth={true}
          variant="indicator"
        />
      )}
    </div>
  );
};

export default FilterPanel;
