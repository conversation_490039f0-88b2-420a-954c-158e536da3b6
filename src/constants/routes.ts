export const ROUTES = {
  // Base app routes
  HOME: '/',
  AGENTS: '/agents',
  AGENTS_DETAILS: (suiteId: string) => `/agents/${suiteId}`,
  PRICING: '/pricing',
  TERMS: '/terms',
  PRODUCTS: '/products',
  ABOUT: '/about',
  OUR_STORY: '/our-story',
  SIGNUP: '/signup',
  LOGIN: '/login',
  FORGOT_PASSWORD: '/forgot-password',
  RESET_PASSWORD: '/reset-password',
  NEW_PASSWORD: (token: string) => `/new-password/${token}`,
  REDIRECT_HANDLER: '/redirect-handler',

  // Dashboard base (main layout)
  DASHBOARD_BASE: '/dashboard',

  // Dashboard main sections
  DASHBOARD_AI_AGENTS: '/dashboard/ai-agents',
  DASHBOARD_AGENT_SUITE: (suiteId: string) =>
    `/dashboard/ai-agents/suite/${suiteId}`,
  DASHBOARD_AGENT_ACTIVATION: '/dashboard/ai-agents/activate',
  DASHBOARD_AGENT_ACTIVATION_SUITE: (suiteId: string) =>
    `/dashboard/ai-agents/activate/suite/${suiteId}`,
  DASHBOARD_AGENT_ACTIVATION_AGENT: (agentId: string) =>
    `/dashboard/ai-agents/activate/agent/${agentId}`,

  // Business Stack routes
  DASHBOARD_BUSINESS_STACK: '/dashboard/business-stack',
  DASHBOARD_BUSINESS_STACK_SELECT_AGENT:
    '/dashboard/business-stack/select-agent',
  DASHBOARD_BUSINESS_STACK_ACTIVATE_SUITE: (suiteId: string) =>
    `/dashboard/business-stack/activate-suite/${suiteId}`,
  DASHBOARD_BUSINESS_STACK_WITH_APP: (appKey: string) =>
    `/dashboard/business-stack/${appKey}`,
  DASHBOARD_BUSINESS_STACK_SALESFORCE_HELP:
    '/dashboard/business-stack/salesforce-help',

  // Knowledge Base routes
  DASHBOARD_KNOWLEDGE_BASE: '/dashboard/knowledge-base',
  DASHBOARD_KNOWLEDGE_BASE_SELECT_AGENT:
    '/dashboard/knowledge-base/select-agent',
  DASHBOARD_KNOWLEDGE_BASE_ACTIVATE_SUITE: (suiteId: string) =>
    `/dashboard/knowledge-base/activate-suite/${suiteId}`,

  // Knowledge Base routes
  DASHBOARD_KNOWLEDGE_BASE_CATEGORY: (categoryId: string) =>
    `/dashboard/knowledge-base/${categoryId}`,
  DASHBOARD_KNOWLEDGE_BASE_ITEM: (categoryId: string, itemId: string) =>
    `/dashboard/knowledge-base/${categoryId}/${itemId}`,

  // Analytics section (renamed from conflicting "Dashboard")
  DASHBOARD_SELECT_AGENT: '/dashboard/analytics/select-agent',
  DASHBOARD_ANALYTICS_SUITE_DETAILS: (suiteId: string) =>
    `/dashboard/analytics/suite/${suiteId}`,
  DASHBOARD_ANALYTICS_ACTIVATE_SUITE: (suiteId: string) =>
    `/dashboard/analytics/activate-suite/${suiteId}`,

  DASHBOARD_ANALYTICS_BASE: '/dashboard/analytics',

  // New analytics structure
  DASHBOARD_ANALYTICS_INSIGHTS: '/dashboard/analytics/insights',
  DASHBOARD_ANALYTICS_INSIGHTS_ALL: '/dashboard/analytics/insights/all',
  DASHBOARD_ANALYTICS_TASK_LOGS: '/dashboard/analytics/task-logs',
  DASHBOARD_ANALYTICS_TASK_LOG_DETAILS: (taskId: string) =>
    `/dashboard/analytics/task-logs/${taskId}`,
  DASHBOARD_ANALYTICS_ASSIGNMENT_LOGS: '/dashboard/analytics/assignment-logs',
  DASHBOARD_ANALYTICS_ASSIGNMENT_LOG_DETAILS: (id: string) =>
    `/dashboard/analytics/assignment-logs/${id}`,

  // Settings section
  DASHBOARD_SETTINGS: '/dashboard/settings',
  DASHBOARD_SETTINGS_PROFILE: '/dashboard/settings/profile',
  DASHBOARD_SETTINGS_NOTIFICATIONS: '/dashboard/settings/notifications',
  DASHBOARD_SETTINGS_BILLING: '/dashboard/settings/billing',
  DASHBOARD_SETTINGS_MEMBERS: '/dashboard/settings/members',
  SETTINGS_CHANGE_EMAIL: '/dashboard/settings/profile/change-email',
  SETTINGS_CHANGE_PASSWORD: '/dashboard/settings/profile/change-password',

  DASHBOARD_SETTINGS_MEMBERS_INVITE: '/dashboard/settings/members/invite',
  DASHBOARD_SETTINGS_COMPANY_INFO: '/dashboard/settings/company-info',
  DASHBOARD_SETTINGS_DEPARTMENT_INFO: '/dashboard/settings/department-info',

  // Members section
  DASHBOARD_MEMBERS: '/dashboard/members',
  DASHBOARD_MEMBERS_INVITE: '/dashboard/members/invite',
  DASHBOARD_MEMBERS_REQUEST_TO_JOIN: '/dashboard/members/request-to-join',
} as const;

// Route paths for React Router (without leading slash for children routes)
export const ROUTE_PATHS = {
  // Base app routes
  HOME: '',
  AGENTS: 'agents',
  AGENTS_DETAILS: 'agents/:agentId',
  SIGNUP: 'signup',
  PRICING: 'pricing',
  TERMS: 'terms',
  LOGIN: 'login',
  RESET_PASSWORD: 'reset-password',
  NEW_PASSWORD: 'new-password/:token',
  REDIRECT_HANDLER: 'redirect-handler',

  // Dashboard routes (for use in children arrays)
  DASHBOARD_AI_AGENTS: 'ai-agents',
  DASHBOARD_AGENT_SUITE: 'ai-agents/suite/:suiteId',
  DASHBOARD_AGENT_ACTIVATION: 'ai-agents/activate',
  DASHBOARD_AGENT_ACTIVATION_SUITE: 'ai-agents/activate/suite/:suiteId',
  DASHBOARD_AGENT_ACTIVATION_AGENT: 'ai-agents/activate/agent/:agentId',

  // Business Stack routes
  DASHBOARD_BUSINESS_STACK: 'business-stack',
  DASHBOARD_BUSINESS_STACK_SELECT_AGENT: 'business-stack/select-agent',
  DASHBOARD_BUSINESS_STACK_ACTIVATE_SUITE:
    'business-stack/activate-suite/:suiteId',
  DASHBOARD_BUSINESS_STACK_WITH_APP: 'business-stack/:appKey',
  DASHBOARD_BUSINESS_STACK_SALESFORCE_HELP: 'business-stack/salesforce-help',

  // Knowledge Base routes
  DASHBOARD_KNOWLEDGE_BASE: 'knowledge-base',
  DASHBOARD_KNOWLEDGE_BASE_SELECT_AGENT: 'knowledge-base/select-agent',
  DASHBOARD_KNOWLEDGE_BASE_ACTIVATE_SUITE:
    'knowledge-base/activate-suite/:suiteId',

  // Knowledge Base routes (for use in children arrays)
  KNOWLEDGE_BASE_CATEGORY: 'knowledge-base/:categoryId',
  KNOWLEDGE_BASE_ITEM: 'knowledge-base/:categoryId/:itemId',

  // Analytics routes (for use in children arrays)
  DASHBOARD_ANALYTICS_BASE: 'analytics',
  DASHBOARD_SELECT_AGENT: 'analytics/select-agent',
  DASHBOARD_ANALYTICS_SUITE_DETAILS: 'analytics/suite/:suiteId',
  DASHBOARD_ANALYTICS_ACTIVATE_SUITE: 'analytics/activate-suite/:suiteId',
  DASHBOARD_ANALYTICS_SCYRA: 'analytics/scyra',

  // New analytics structure
  DASHBOARD_ANALYTICS_INSIGHTS: 'analytics/insights',
  DASHBOARD_ANALYTICS_INSIGHTS_ALL: 'analytics/insights/all',
  DASHBOARD_ANALYTICS_TASK_LOGS: 'analytics/task-logs',
  DASHBOARD_ANALYTICS_ASSIGNMENT_LOGS: 'analytics/assignment-logs',
  DASHBOARD_ANALYTICS_ASSIGNMENT_LOG_DETAILS: 'analytics/assignment-logs/:id',

  // Settings routes (for use in children arrays)
  DASHBOARD_SETTINGS: 'settings',
  DASHBOARD_SETTINGS_PROFILE: 'settings/profile',
  DASHBOARD_SETTINGS_NOTIFICATIONS: 'settings/notifications',
  DASHBOARD_SETTINGS_BILLING: 'settings/billing',
  DASHBOARD_SETTINGS_MEMBERS: 'settings/members',
  DASHBOARD_SETTINGS_MEMBERS_INVITE: 'settings/members/invite',
  DASHBOARD_SETTINGS_COMPANY_INFO: 'settings/company-info',
  DASHBOARD_SETTINGS_DEPARTMENT_INFO: 'settings/department-info',

  // Members routes (for use in children arrays)
  DASHBOARD_MEMBERS: 'members',
  DASHBOARD_MEMBERS_INVITE: 'members/invite',
  DASHBOARD_MEMBERS_REQUEST_TO_JOIN: 'members/request-to-join',
} as const;
