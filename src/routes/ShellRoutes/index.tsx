import { lazy } from 'react';
import { Navigate } from 'react-router-dom';

import { withSuspense } from '@/components/hocs/suspense/withSuspense';
import { retryChunkLoad } from '@/utils/chunkErrorHandler';

// Lazy load public pages with chunk error handling
const HomePage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/HomePage')))
);
const OurStoryPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/OurStoryPage')))
);

const LoginPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/LoginPage')))
);

const SignupPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/SignupPage')))
);

const PricingPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/PricingPage')))
);

const TermsAndConditionsPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/TermsAndConditionPage')))
);

const AgentsPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/AgentPage/Marketplace')))
);

const AgentDetailsPage = withSuspense(
  lazy(() => retryChunkLoad(() => import('../../pages/AgentPage/AgentDetails')))
);

export const ShellRoutes = [
  {
    index: true,
    element: <HomePage />,
  },
  {
    path: 'our-story',
    element: <OurStoryPage />,
  },
  {
    path: 'agents',
    element: <AgentsPage />,
  },
  {
    path: 'agents/:agentId',
    element: <AgentDetailsPage />,
  },
  {
    path: 'login',
    element: <LoginPage />,
  },
  {
    path: 'signup',
    element: <SignupPage />,
  },
  {
    path: 'pricing',
    element: <PricingPage />,
  },
  {
    path: 'terms',
    element: <TermsAndConditionsPage />,
  },
  {
    path: '*',
    element: <Navigate to="/" replace />,
  },
];
