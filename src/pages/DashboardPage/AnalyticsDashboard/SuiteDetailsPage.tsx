import clsx from 'clsx';
import { ArrowLeft } from 'lucide-react';
import React, { useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import EnhancedChatSidebar from '@/components/common/EnhancedChatSidebar';
import { useTenant } from '@/context/TenantContext';
import { useGetAIAgentSuites } from '@/hooks/useAgents';
import { useMediaQuery } from '@/hooks/useMediaQuery';

import { Icons } from '../../../assets/icons/DashboardIcons';
import { dashboardIcons, dashboardIconsMobile } from '../../../assets/images';
import AgentSuiteSkeletonLoader from '../../../components/ui/AgentSuiteSkeleton';
import { ROUTES } from '../../../constants/routes';
import { AIAgent } from '../../../types/agents';
import { AgentCard } from '../../AiAgentsPage';

const SuiteDetailsPage: React.FC = () => {
  const navigate = useNavigate();
  const { suiteId } = useParams<{ suiteId: string }>();
  const { setActiveAgent, activeAgent } = useTenant();
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);
  const isMobile = useMediaQuery('(max-width: 768px)');
  const { data: agentSuites = [], isLoading } = useGetAIAgentSuites();

  // Find the specific suite
  const suite = agentSuites.find(s => s.agentSuiteKey === suiteId);

  const handleAgentSelect = (agent: AIAgent) => {
    // Set the active agent
    setActiveAgent(agent.agentKey);

    // Navigate to insights with suite and agent as query parameters
    navigate(
      `${ROUTES.DASHBOARD_ANALYTICS_INSIGHTS}?suite=${suiteId}&agent=${agent.agentKey}`
    );
  };

  const handleBackClick = () => {
    navigate(-1);
  };

  if (isLoading) {
    return (
      <div className="flex h-full">
        <EnhancedChatSidebar reloadChatHistoryRef={reloadChatHistoryRef} />
        <div className="flex-1 p-8">
          <div className="animate-pulse">
            <div className="mb-4 h-8 w-1/4 rounded bg-gray-200"></div>
            <div className="mb-6 h-32 rounded bg-gray-200"></div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-24 rounded bg-gray-200"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!suite) {
    return (
      <div className="flex h-full">
        <EnhancedChatSidebar reloadChatHistoryRef={reloadChatHistoryRef} />
        <div className="flex-1 p-8">
          <div className="text-center">
            <h2 className="mb-4 text-2xl font-semibold text-gray-900">
              Suite Not Found
            </h2>
            <p className="mb-6 text-gray-600">
              The requested suite could not be found.
            </p>
            <button
              onClick={handleBackClick}
              className="rounded-lg bg-primary px-4 py-2 text-white hover:bg-primary/90"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      {/* Chat Sidebar */}
      <EnhancedChatSidebar
        reloadChatHistoryRef={reloadChatHistoryRef}
        className="hidden md:block"
      />

      {/* Main Content */}
      <div
        className={clsx(
          'flex flex-1 flex-col gap-4 overflow-y-auto p-4 sm:gap-6 sm:p-8'
        )}
      >
        <div className="flex flex-col gap-4 text-start">
          <div className="mb-2 flex items-center gap-4">
            <button onClick={handleBackClick}>
              <ArrowLeft className="h-5 w-5 text-primary" />
            </button>
            <Icons.Dashboard className="h-6 w-6 text-primary" />
            <h1 className="text-lg font-semibold text-blackOne sm:text-2xl">
              Dashboard
            </h1>
          </div>

          {/* Hero Section */}
          <div
            className={clsx(
              'flex h-fit w-full max-w-3xl items-center overflow-hidden rounded-xl bg-[#040721] bg-cover bg-center text-white sm:h-[140px] sm:rounded-2xl'
            )}
          >
            <div className="flex w-full items-center justify-between">
              <div className="p-4 text-left sm:p-6">
                <h2 className="mb-2 text-sm font-bold text-white sm:text-lg">
                  Actionable Intelligence across all Agentic AI agents
                </h2>
                <p className="text-sm text-white">
                  Compare performance, monitor activity, and act on daily
                  insights.
                </p>
              </div>
            </div>
            <div className="relative mr-4 h-full w-[178px] sm:mr-8">
              <img
                src={isMobile ? dashboardIconsMobile : dashboardIcons}
                alt="bg"
                className="h-full w-full object-contain"
              />
            </div>
          </div>
        </div>

        {/* Activate an agent to begin section */}
        <h3 className="text-base font-medium text-blackOne sm:text-lg">
          {' Select agent -> View Dashboard'}
        </h3>

        {/* Content */}
        <div className="w-full max-w-3xl">
          {isLoading ? (
            <AgentSuiteSkeletonLoader count={4} />
          ) : (
            <div className="grid grid-cols-1 gap-4 sm:gap-6 md:grid-cols-2 lg:grid-cols-2">
              {[...(suite.availableAgents || [])]
                .sort((a, b) =>
                  (a.agentName || '').localeCompare(
                    b.agentName || '',
                    undefined,
                    { sensitivity: 'base' }
                  )
                )
                .map(agent => (
                  <AgentCard
                    key={agent.agentKey}
                    agent={agent}
                    showChatButton={false}
                    isActiveAgent={activeAgent === agent.agentKey}
                    link={
                      agent.agentKey
                        ? `${ROUTES.DASHBOARD_ANALYTICS_INSIGHTS}?suite=${suiteId}&agent=${agent.agentKey}`
                        : '#'
                    }
                    onAgentSelect={() => handleAgentSelect(agent)}
                  />
                ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SuiteDetailsPage;
