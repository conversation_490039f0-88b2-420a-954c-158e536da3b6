import { yupResolver } from '@hookform/resolvers/yup';
import { AxiosError } from 'axios';
import { CheckCircle, Loader2, Pencil, X } from 'lucide-react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import Select from 'react-select';

import { profilePlaceholder } from '@/assets/images';
import { Spinner } from '@/components/common/Loader';
import { DashboardWithChatLayout } from '@/components/layout/DashboardWithChatLayout';
import { Input, NotificationContainer } from '@/components/ui';
import { AvatarUploadSuccess } from '@/components/ui/AvatarUploadSuccess';
import { useTenant } from '@/context/TenantContext';

import { useAuth } from '../../../context/AuthContext';
import { useAvatarUpload } from '../../../hooks/useAvatarUpload';
import { useNotifications } from '../../../hooks/useNotifications';
import { useTimezones } from '../../../hooks/useTimezones';
import {
  useGetUserFunctions,
  useUpdateUserInfoMutation,
} from '../../../hooks/useUserProfile';
import { profileSettingsSchema } from '../../../lib/yup/profileValidations';
import { ProfileFormData } from '../../../types/profile';
import { AccountInformation } from './AccountInformation';

export const ProfileForm: React.FC = () => {
  const { user, isLoadingUserInitials, isError } = useAuth();
  const { setActiveAgent } = useTenant();
  const { notifyCustom, notifications, dismiss } = useNotifications();
  const updateUserInfoMutation = useUpdateUserInfoMutation();
  const {
    isUploading,
    uploadError,
    uploadProgress,
    uploadSuccess,
    handleFileSelect,
    previewUrl,
  } = useAvatarUpload();
  const {
    timezoneOptions: apiTimezoneOptions,
    isLoading: isLoadingTimezones,
    isError: isTimezoneError,
  } = useTimezones();

  // Fetch user functions/roles from API
  const {
    data: userFunctionsData,
    isLoading: isLoadingUserFunctions,
    isError: isUserFunctionsError,
  } = useGetUserFunctions();

  const fileInputRef = useRef<HTMLInputElement>(null);
  const reloadChatHistoryRef = useRef<(() => Promise<void>) | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [chatMessage] = useState<string>('');

  // Helper functions for notifications
  const showError = (message: string) => {
    notifyCustom({
      message,
      type: 'error',
    });
  };

  const showSuccess = (message: string) => {
    notifyCustom({
      message,
      type: 'success',
    });
  };

  const userData = user;

  const getInitialValues = useCallback(
    () => ({
      firstName: userData?.firstName || '',
      lastName: userData?.lastName || '',
      email: userData?.email || '',
      role: userData?.roleInCompany || '',
      timezone: userData?.timezone || '', // No default timezone
    }),
    [userData]
  );

  // Personal information form configuration
  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
  } = useForm<ProfileFormData>({
    resolver: yupResolver(profileSettingsSchema),
    defaultValues: getInitialValues(),
  });

  // Set active agent to Regis when component mounts
  useEffect(() => {
    setActiveAgent('regis');
  }, [setActiveAgent]);

  // Update forms when user data or tenant data changes
  useEffect(() => {
    if (userData) {
      reset(getInitialValues());
    }
  }, [userData, reset, getInitialValues]);

  // Personal information form submission handler
  const onSubmit: SubmitHandler<ProfileFormData> = async data => {
    setIsSubmitting(true);
    try {
      await updateUserInfoMutation.mutateAsync({
        firstname: data.firstName,
        lastname: data.lastName,
        timezone: data.timezone,
        roleInCompany: data.role,
      });

      showSuccess('Profile updated successfully!');

      // Reset form dirty state after successful submission
      reset(data);
    } catch (error) {
      let errorMessage = 'Failed to update profile';

      if (error instanceof AxiosError && error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      showError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Dynamic role options from API
  const roleOptions = userFunctionsData?.data
    ? userFunctionsData.data.map((role: string) => ({
        value: role,
        label: role.charAt(0).toUpperCase() + role.slice(1).toLowerCase(),
      }))
    : [];

  // Fallback timezone options if API fails
  const fallbackTimezoneOptions: { value: string; label: string }[] = [];

  const timezoneOptions = isTimezoneError
    ? fallbackTimezoneOptions
    : apiTimezoneOptions;

  // Custom styles for react-select
  const selectStyles = {
    control: (provided: any) => ({
      ...provided,
      height: '40px',
      border: '1px solid #DFEAF2',
      borderRadius: '6px',
      boxShadow: 'none',
      '&:hover': {
        border: '1px solid #DFEAF2',
      },
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? '#FF5C02'
        : state.isFocused
          ? '#FFF5F0'
          : 'white',
      color: state.isSelected ? 'white' : '#374151',
      '&:hover': {
        backgroundColor: state.isSelected ? '#FF5C02' : '#FFF5F0',
      },
    }),
    menu: (provided: any) => ({
      ...provided,
      border: '1px solid #DFEAF2',
      borderRadius: '6px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    }),
  };

  if (isLoadingUserInitials) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Spinner className="h-8 w-8" />
          <p className="text-sm text-gray-500">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
            <svg
              className="h-8 w-8 text-red-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="mb-2 text-lg font-medium text-blackOne">
            Failed to load profile
          </h3>
          <p className="mb-4 text-sm text-gray-500">
            Unable to load your profile information. Please try refreshing the
            page.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="rounded-md bg-primary px-4 py-2 text-sm text-white hover:bg-primary/90"
          >
            Refresh Page
          </button>
        </div>
      </div>
    );
  }

  return (
    <DashboardWithChatLayout
      reloadChatHistoryRef={reloadChatHistoryRef}
      externalMessage={chatMessage}
    >
      <div className="px-4 py-6 sm:px-8">
        <NotificationContainer
          notifications={notifications}
          onClose={dismiss}
          className="w-full"
        />
        <div className="space-y-8">
          {/* Personal Information Section */}
          <div>
            <h2 className="mb-6 text-lg font-medium text-blackOne sm:text-xl sm:font-semibold">
              Personal Information
            </h2>

            <div className="flex flex-col items-start space-y-6">
              {/* Profile Picture */}
              <div className="flex w-full flex-col items-start gap-2">
                <div className="relative shrink-0">
                  <AvatarUploadSuccess
                    src={
                      previewUrl ||
                      userData?.profilePicture ||
                      profilePlaceholder
                    }
                    alt="Profile"
                    fallback={userData?.firstName?.[0] || 'U'}
                    isSuccess={uploadSuccess}
                    isUploading={isUploading}
                    uploadProgress={uploadProgress}
                    onAnimationComplete={() => {}}
                  />

                  <button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploading}
                    className="absolute bottom-2 right-2 flex h-[28px] w-[28px] flex-shrink-0 items-center justify-center rounded-full bg-primary text-xs text-white transition-colors hover:bg-primary/90 disabled:cursor-not-allowed disabled:bg-gray-400"
                  >
                    <Pencil className="h-3 w-3" />
                  </button>

                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={async e => {
                      await handleFileSelect(e);
                      // Clear the input to allow selecting the same file again
                      e.target.value = '';
                    }}
                    className="hidden"
                  />
                </div>

                {uploadError && (
                  <div className="flex items-center gap-2 rounded-md bg-red-50 px-2 py-1">
                    <X className="h-3 w-3 text-red-500" />
                    <p className="text-xs text-red-600">{uploadError}</p>
                  </div>
                )}

                {uploadSuccess && (
                  <div className="bg-green-50 flex animate-fade-in items-center gap-2 rounded-md px-2 py-1">
                    <CheckCircle className="text-green-500 h-3 w-3" />
                    <p className="text-green-600 text-xs">
                      Avatar updated successfully!
                    </p>
                  </div>
                )}
              </div>

              {/* Personal Information Form */}
              <form
                onSubmit={handleSubmit(onSubmit)}
                className="w-full max-w-2xl space-y-4"
              >
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <label className="mb-2 block text-[13px] text-subText">
                      First name
                    </label>
                    <Controller
                      name="firstName"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="text"
                          className={`h-10 w-full rounded-md border px-3 py-2 focus:outline-none ${
                            errors.firstName
                              ? 'border-red-500'
                              : 'border-[#DFEAF2]'
                          }`}
                          placeholder="Enter first name"
                        />
                      )}
                    />
                    {errors.firstName && (
                      <p className="mt-1 text-xs text-red-500">
                        {errors.firstName.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="mb-2 block text-[13px] text-subText">
                      Job Function
                    </label>
                    <Controller
                      name="role"
                      control={control}
                      render={({ field }) => (
                        <Select
                          options={roleOptions}
                          styles={{
                            ...selectStyles,
                            control: (provided: any) => ({
                              ...provided,
                              height: '40px',
                              border: errors.role
                                ? '1px solid #ef4444'
                                : '1px solid #DFEAF2',
                              borderRadius: '6px',
                              boxShadow: 'none',
                              '&:hover': {
                                border: errors.role
                                  ? '1px solid #ef4444'
                                  : '1px solid #DFEAF2',
                              },
                            }),
                          }}
                          value={
                            roleOptions.find(
                              (option: { value: string; label: string }) =>
                                option.value === field.value
                            ) || null
                          }
                          onChange={(
                            selectedOption: {
                              value: string;
                              label: string;
                            } | null
                          ) => {
                            field.onChange(selectedOption?.value || '');
                          }}
                          components={{
                            IndicatorSeparator: () => null,
                          }}
                          placeholder={
                            isLoadingUserFunctions
                              ? 'Loading roles...'
                              : isUserFunctionsError
                                ? 'Select role (limited options)'
                                : 'Select role'
                          }
                          isSearchable={false}
                          isLoading={isLoadingUserFunctions}
                        />
                      )}
                    />
                    {errors.role && (
                      <p className="mt-1 text-xs text-red-500">
                        {errors.role.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <label className="mb-2 block text-[13px] text-subText">
                      Last name
                    </label>
                    <Controller
                      name="lastName"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="text"
                          className={`h-10 w-full rounded-md border px-3 py-2 focus:outline-none ${
                            errors.lastName
                              ? 'border-red-500'
                              : 'border-[#DFEAF2]'
                          }`}
                          placeholder="Enter last name"
                        />
                      )}
                    />
                    {errors.lastName && (
                      <p className="mt-1 text-xs text-red-500">
                        {errors.lastName.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="mb-2 block text-[13px] text-subText">
                      Timezone
                    </label>
                    <Controller
                      name="timezone"
                      control={control}
                      render={({ field }) => (
                        <Select
                          options={timezoneOptions}
                          styles={{
                            ...selectStyles,
                            control: (provided: any) => ({
                              ...provided,
                              height: '40px',
                              border: errors.timezone
                                ? '1px solid #ef4444'
                                : '1px solid #DFEAF2',
                              borderRadius: '6px',
                              boxShadow: 'none',
                              '&:hover': {
                                border: errors.timezone
                                  ? '1px solid #ef4444'
                                  : '1px solid #DFEAF2',
                              },
                            }),
                          }}
                          value={timezoneOptions.find(
                            (option: { value: string; label: string }) =>
                              option.value === field.value
                          )}
                          onChange={selectedOption =>
                            field.onChange(selectedOption?.value)
                          }
                          components={{
                            IndicatorSeparator: () => null,
                          }}
                          placeholder={
                            isLoadingTimezones
                              ? 'Loading timezones...'
                              : isTimezoneError
                                ? 'Select timezone (limited options)'
                                : 'Select timezone'
                          }
                          isSearchable={true}
                        />
                      )}
                    />
                    {errors.timezone && (
                      <p className="mt-1 text-xs text-red-500">
                        {errors.timezone.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex justify-start">
                  <button
                    type="submit"
                    disabled={!isDirty || isSubmitting}
                    className="h-10 w-fit rounded-lg bg-grayTen px-6 py-2 text-white transition-colors hover:bg-gray-900 disabled:opacity-50"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center justify-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />{' '}
                        <span className="text-sm">Saving...</span>
                      </div>
                    ) : (
                      'Save changes'
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* account information section */}
          <AccountInformation email={userData?.email || ''} />
        </div>
      </div>
    </DashboardWithChatLayout>
  );
};
