import clsx from 'clsx';
import { Loader2, Search } from 'lucide-react';
import React, { useState } from 'react';

import Pagination from '@/components/common/Pagination';
import { Input } from '@/components/ui';
import { useDebounce } from '@/hooks/useDebounce';
import {
  useCancelSuiteInviteMutation,
  useInviteSuiteMemberMutation,
  useSuiteInvites,
  useUpdateSuiteInviteRoleMutation,
} from '@/hooks/useMembers';
import { useGetUserProfile } from '@/hooks/useUserProfile';

import ActionDropdown from '../../../components/common/ActionDropdown';
import DataTable, { Column } from '../../../components/ui/tables/DataTable';
import {
  Invitation,
  mapUiRoleToApiRole,
  MemberRole,
  SuiteMemberRoleApi,
} from '../../../types/members';
import {
  generateMemberInitials,
  getRoleColor,
  getRoleDisplayName,
} from '../../../utils/members';
import CancelInvitationModal from './CancelInvitation';
import UpdateRoleModal from './UpdateRole';

interface InvitesTabProps {
  agentSuiteKey: string;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onShowAlert: (message: string, type: 'error' | 'success' | 'warning') => void;
}

export const InvitesTab: React.FC<InvitesTabProps> = ({
  agentSuiteKey,
  searchQuery,
  onSearchChange,
  onShowAlert,
}) => {
  const [page, setPage] = useState<number>(1);
  const [pageSize] = useState<number>(10);
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const resendInvitationMutation = useInviteSuiteMemberMutation();

  const [isUpdateRoleModalOpen, setIsUpdateRoleModalOpen] = useState(false);
  const [isCancelInvitationModalOpen, setIsCancelInvitationModalOpen] =
    useState(false);
  const [selectedInvitation, setSelectedInvitation] =
    useState<Invitation | null>(null);

  const { data: userData } = useGetUserProfile();

  const { data: invitationsData, isLoading: isLoadingInvitations } =
    useSuiteInvites(
      agentSuiteKey,
      debouncedSearchQuery,
      page,
      pageSize,
      !!agentSuiteKey
    );

  const invitations = invitationsData?.invites || [];

  const updateInvitationRoleMutation = useUpdateSuiteInviteRoleMutation();
  const cancelInvitationMutation = useCancelSuiteInviteMutation();

  const handleUpdateInvitationRole = async (
    invitationId: string,
    newRole: MemberRole
  ) => {
    try {
      await updateInvitationRoleMutation.mutateAsync({
        inviteId: invitationId,
        payload: {
          agentSuiteKey: agentSuiteKey,
          memberRole: mapUiRoleToApiRole(newRole),
        },
      });
      onShowAlert('Role updated successfully', 'success');
      setIsUpdateRoleModalOpen(false);
      setSelectedInvitation(null);
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message || 'Failed to update role';
      onShowAlert(errorMessage, 'error');
      setIsUpdateRoleModalOpen(false);
      setSelectedInvitation(null);
    }
  };

  const handleCancelInvitation = async (invitation: Invitation) => {
    try {
      await cancelInvitationMutation.mutateAsync({
        inviteId: invitation.id,
        agentSuiteKey: agentSuiteKey,
      });
      onShowAlert(
        `Invitation to ${invitation.email} has been cancelled`,
        'success'
      );
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message || 'Failed to cancel invitation';
      onShowAlert(errorMessage, 'error');
    } finally {
      setIsCancelInvitationModalOpen(false);
      setSelectedInvitation(null);
    }
  };

  const resendInvitation = async (invitation: Invitation) => {
    try {
      await resendInvitationMutation.mutateAsync({
        agentSuiteKey: agentSuiteKey,
        firstname: invitation.firstname.trim(),
        lastname: invitation.lastname.trim(),
        email: invitation.email.trim(),
        role: invitation.role as SuiteMemberRoleApi,
      });
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message || 'Failed to resend invitation';
      onShowAlert(errorMessage, 'error');
    }
  };

  // Invitation columns
  const invitationColumns: Column<Invitation>[] = [
    {
      key: 'email',
      label: 'Name',
      render: (_, invitation) => (
        <div className="flex items-center">
          <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-blackOne text-sm font-medium text-white">
            {generateMemberInitials(invitation.firstname)}
            {generateMemberInitials(invitation.lastname)}
          </div>
          <div>
            <div className="font-medium text-blackOne">
              {invitation.firstname} {invitation.lastname}
            </div>
            <div className="text-sm text-subText">{invitation.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'role',
      label: 'Role',
      render: (_, invitation) => (
        <span
          className={`flex h-[34px] w-28 items-center justify-center rounded-lg px-4 text-xs font-medium ${
            getRoleColor(invitation.role) || 'bg-gray-30 text-white'
          }`}
        >
          {getRoleDisplayName(invitation.role) || 'N/A'}
        </span>
      ),
    },
    {
      key: 'id',
      label: '',
      render: (_, invitation) => {
        const actions = [
          {
            label: 'Change Role',
            icon: null,
            onClick: () => {
              setSelectedInvitation(invitation);
              setIsUpdateRoleModalOpen(true);
            },
            variant: 'default' as const,
          },
          {
            label: resendInvitationMutation.isPending
              ? 'Sending...'
              : resendInvitationMutation.isSuccess
                ? (() => {
                    // Show "Invitation Sent" for 3 seconds, then revert to "Resend Invitation"
                    if (resendInvitationMutation.isSuccess) {
                      setTimeout(() => {
                        resendInvitationMutation.reset &&
                          resendInvitationMutation.reset();
                      }, 3000);
                      return 'Invitation Sent';
                    }
                    return 'Invitation Sent';
                  })()
                : 'Resend Invitation',
            icon: resendInvitationMutation.isPending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : null,
            onClick: () => {
              resendInvitation(invitation);
            },

            isDisabled: resendInvitationMutation.isPending,
            isLoading: resendInvitationMutation.isPending,
            variant: (() => {
              // Show "Invitation Sent" for 3 seconds, then revert to "Resend Invitation"
              if (resendInvitationMutation.isSuccess) {
                setTimeout(() => {
                  resendInvitationMutation.reset &&
                    resendInvitationMutation.reset();
                }, 3000);
                return 'success' as const;
              }
              return 'default' as const;
            })(),
            closeOnClick: false,
          },
          {
            label: 'Cancel Invitation',
            icon: null,
            onClick: () => {
              setSelectedInvitation(invitation);
              setIsCancelInvitationModalOpen(true);
            },
            variant: 'danger' as const,
          },
        ];

        const currentUserRole =
          userData?.userInfo?.tenant?.claimedAgentSuites
            ?.find(suite => suite?.suite?.agentSuiteKey === agentSuiteKey)
            ?.members?.find(m => m?.user?.userId === userData?.userInfo?.userId)
            ?.memberRoles?.[0] || 'MEMBER';

        const canManageMembers =
          currentUserRole === 'MANAGER' || currentUserRole === 'LEAD';

        return canManageMembers ? <ActionDropdown actions={actions} /> : null;
      },
      className: 'text-right',
    },
  ];

  return (
    <>
      <div className="sm:space-y-6">
        {/* Search */}
        <div className="flex items-center gap-3">
          <Input
            type="text"
            placeholder="Search by name or email"
            className="h-10 w-[300px] rounded-[10px] border border-grayThirteen py-2 pl-4 pr-10 text-sm placeholder:text-grayTen focus:outline-none focus:ring-0"
            endIcon={<Search className="mt-0.5 h-4 w-4" />}
            value={searchQuery}
            onChange={e => onSearchChange(e.target.value)}
          />
        </div>

        {/* Desktop Table - Hidden on mobile */}
        <div className="hidden flex-col gap-4 overflow-x-auto sm:flex">
          <DataTable<Invitation & Record<string, unknown>>
            data={
              invitations as unknown as (Invitation & Record<string, unknown>)[]
            }
            columns={
              invitationColumns as unknown as Column<
                Invitation & Record<string, unknown>
              >[]
            }
            loading={isLoadingInvitations}
            emptyMessage="No invitations found"
            rowColoring={true}
            rowColoringType="odd"
            getRowId={invitation => invitation.id}
          />
          {!isLoadingInvitations && invitations && invitations?.length > 0 && (
            <Pagination
              currentPage={page}
              totalPages={
                invitationsData
                  ? Math.ceil(invitationsData.total / invitationsData.pageSize)
                  : 0
              }
              onPageChange={page => setPage(page)}
            />
          )}
        </div>

        {/* Mobile Card View - Hidden on desktop */}
        <div className="-mx-4 flex flex-col sm:hidden">
          {isLoadingInvitations ? (
            // Loading state
            Array.from({ length: 3 }).map((_, index) => (
              <div
                key={index}
                className={clsx('px-6 py-4', index % 2 === 0 && 'bg-[#FFF1EB]')}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-gray-200" />
                    <div className="space-y-2">
                      <div className="h-4 w-32 rounded bg-gray-200" />
                      <div className="h-3 w-40 rounded bg-gray-200" />
                    </div>
                  </div>
                  <div className="h-6 w-6 rounded bg-gray-200" />
                </div>
                <div className="mt-4 space-y-2">
                  <div className="h-3 w-20 rounded bg-gray-200" />
                  <div className="h-3 w-24 rounded bg-gray-200" />
                </div>
              </div>
            ))
          ) : invitations && invitations.length > 0 ? (
            <>
              {invitations.map((invitation, index) => {
                const currentUserRole =
                  userData?.userInfo?.tenant?.claimedAgentSuites
                    ?.find(
                      suite => suite?.suite?.agentSuiteKey === agentSuiteKey
                    )
                    ?.members?.find(
                      m => m?.user?.userId === userData?.userInfo?.userId
                    )
                    ?.memberRoles?.[0]?.toLowerCase() || 'member';

                const canManageMembers =
                  currentUserRole === 'manager' || currentUserRole === 'lead';

                const actions = [
                  {
                    label: 'Change Role',
                    icon: null,
                    onClick: () => {
                      setSelectedInvitation(invitation);
                      setIsUpdateRoleModalOpen(true);
                    },
                    variant: 'default' as const,
                  },
                  {
                    label: resendInvitationMutation.isPending
                      ? 'Sending...'
                      : resendInvitationMutation.isSuccess
                        ? (() => {
                            if (resendInvitationMutation.isSuccess) {
                              setTimeout(() => {
                                resendInvitationMutation.reset &&
                                  resendInvitationMutation.reset();
                              }, 3000);
                              return 'Invitation Sent';
                            }
                            return 'Invitation Sent';
                          })()
                        : 'Resend Invitation',
                    icon: resendInvitationMutation.isPending ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : null,
                    onClick: () => {
                      resendInvitation(invitation);
                    },
                    isDisabled: resendInvitationMutation.isPending,
                    isLoading: resendInvitationMutation.isPending,
                    variant: (() => {
                      if (resendInvitationMutation.isSuccess) {
                        setTimeout(() => {
                          resendInvitationMutation.reset &&
                            resendInvitationMutation.reset();
                        }, 3000);
                        return 'success' as const;
                      }
                      return 'default' as const;
                    })(),
                    closeOnClick: false,
                  },
                  {
                    label: 'Cancel Invitation',
                    icon: null,
                    onClick: () => {
                      setSelectedInvitation(invitation);
                      setIsCancelInvitationModalOpen(true);
                    },
                    variant: 'danger' as const,
                  },
                ];

                return (
                  <div
                    key={invitation.id}
                    className={clsx('p-4', index % 2 === 0 && 'bg-[#FFF1EB]')}
                  >
                    {/* Header: Avatar, Name, Email, and Actions */}
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-blackOne text-sm font-medium text-white">
                          {generateMemberInitials(invitation.firstname)}
                          {generateMemberInitials(invitation.lastname)}
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className="truncate font-medium text-blackOne">
                            {invitation.firstname} {invitation.lastname}
                          </div>
                          <div className="truncate text-sm text-subText">
                            {invitation.email}
                          </div>
                        </div>
                      </div>
                      {canManageMembers && <ActionDropdown actions={actions} />}
                    </div>

                    {/* Role */}
                    <div className="mt-4 flex w-full items-center justify-between gap-2">
                      <span className="text-xs text-subText">Role</span>
                      <span
                        className={clsx(
                          'inline-flex items-center justify-center rounded-lg px-3 py-1.5 text-xs font-medium',
                          getRoleColor(invitation.role)
                        )}
                      >
                        {getRoleDisplayName(invitation.role)}
                      </span>
                    </div>
                  </div>
                );
              })}
              <div className="px-6 py-4">
                <Pagination
                  currentPage={page}
                  totalPages={
                    invitationsData
                      ? Math.ceil(
                          invitationsData.total / invitationsData.pageSize
                        )
                      : 0
                  }
                  onPageChange={page => setPage(page)}
                />
              </div>
            </>
          ) : (
            <div className="px-6 py-8 text-center text-sm text-subText">
              No invitations found
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      <UpdateRoleModal
        isOpen={isUpdateRoleModalOpen}
        onClose={() => {
          setIsUpdateRoleModalOpen(false);
          setSelectedInvitation(null);
        }}
        onUpdateRole={(newRole: MemberRole) => {
          handleUpdateInvitationRole(selectedInvitation?.id || '', newRole);
        }}
        userDetails={
          selectedInvitation
            ? {
                id: selectedInvitation.id,
                name: `${selectedInvitation.firstname} ${selectedInvitation.lastname}`,
                role: selectedInvitation.role as MemberRole,
              }
            : null
        }
        loading={updateInvitationRoleMutation.isPending}
      />

      <CancelInvitationModal
        isOpen={isCancelInvitationModalOpen}
        onClose={() => {
          setIsCancelInvitationModalOpen(false);
          setSelectedInvitation(null);
        }}
        onCancel={handleCancelInvitation}
        invitation={selectedInvitation}
        loading={cancelInvitationMutation.isPending}
      />
    </>
  );
};
