/**
 * Heartbeat API Types and Interfaces
 * 
 * This file contains TypeScript interfaces for the agent heartbeat functionality,
 * including API request/response types and component prop types.
 */

/**
 * Heartbeat status for an agent
 */
export type HeartbeatStatus = 'IDLE' | 'ACTIVE';

/**
 * Individual agent heartbeat info
 */
export interface AgentHeartbeatInfo {
  agentKey: string;
  status: HeartbeatStatus;
}

/**
 * Response from GET /agentous-agentic-service/ai-agents/heartbeats
 */
export interface HeartbeatListResponse {
  status: boolean;
  message: string;
  data: AgentHeartbeatInfo[];
}

/**
 * Response from POST /agentous-agentic-service/ai-agents/initialize-heartbeat
 * and PATCH /agentous-agentic-service/ai-agents/pause-heartbeat
 */
export interface HeartbeatActionResponse {
  status: boolean;
  message: string;
  data: null;
}

/**
 * Error response structure for heartbeat API calls
 */
export interface HeartbeatErrorResponse {
  status: false;
  message: string;
  data: {
    timestamp: string;
    message: string;
    details: string;
  };
}

/**
 * Props for AgentCard component related to heartbeat functionality
 */
export interface HeartbeatControlProps {
  /** Whether to show the heartbeat control button */
  showHeartbeatControl?: boolean;
  /** Current heartbeat status of the agent */
  heartbeatStatus?: HeartbeatStatus | null;
  /** Callback when heartbeat action is triggered */
  onHeartbeatAction?: (action: 'initialize' | 'pause', agentKey: string) => Promise<void>;
  /** Whether heartbeat operation is currently loading */
  isHeartbeatLoading?: boolean;
}

/**
 * State for managing heartbeat data in parent components
 */
export interface HeartbeatState {
  /** Map of agent keys to their heartbeat status */
  heartbeatData: Record<string, HeartbeatStatus>;
  /** Whether heartbeat data is currently being fetched */
  isLoading: boolean;
  /** Error message if heartbeat fetch failed */
  error: string | null;
  /** Agent key currently performing heartbeat action */
  loadingAgent: string | null;
}

/**
 * Hook return type for heartbeat management
 */
export interface UseHeartbeatReturn {
  /** Current heartbeat state */
  heartbeatState: HeartbeatState;
  /** Function to fetch all agents' heartbeat status */
  fetchHeartbeats: () => Promise<void>;
  /** Function to initialize heartbeat for a specific agent */
  initializeHeartbeat: (agentKey: string) => Promise<void>;
  /** Function to pause heartbeat for a specific agent */
  pauseHeartbeat: (agentKey: string) => Promise<void>;
  /** Function to get heartbeat status for a specific agent */
  getHeartbeatStatus: (agentKey: string) => HeartbeatStatus | null;
}
